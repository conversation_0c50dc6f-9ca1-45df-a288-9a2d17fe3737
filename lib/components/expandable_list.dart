import 'package:flutter/material.dart';
import 'package:get/get.dart';

abstract class LevelItem {
  String id;
  RxList<LevelItem> children;
  RxnBool checked = RxnBool();

  LevelItem(this.id, this.children, this.checked);
}

class ExpandableList<T extends LevelItem> extends StatefulWidget {
  final RxList<T> items;
  final Widget Function(int level, T item) builder;
  final Function(T item) onSelectedItem;

  const ExpandableList({super.key, required this.items, required this.builder, required this.onSelectedItem});

  @override
  createState() => _ExpandableListState<T>();
}

class _ExpandableListState<T extends LevelItem> extends State<ExpandableList<T>> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: widget.items.stream,
      builder: (context, snapshot) {
        return ReorderableListView(
          onReorder: (int oldIndex, int newIndex) {
            final item = widget.items.removeAt(oldIndex);
            widget.items.insert(newIndex, item);
          },
          children: widget.items.map((item) => buildExpansionTile(0, item)).toList(),
        );
      },
    );
  }

  Widget buildExpansionTile(int level, T item) {
    return StreamBuilder(
      key: ValueKey(item.id), // 给 StreamBuilder 添加 key
      stream: item.children.stream,
      builder: (context, snapshot) {
        if (item.children.isEmpty) {
          return IntrinsicHeight(
            key: ValueKey('${item.id}_tile'),
            child: ListTile(
              title: widget.builder(level, item),
              onTap: () => widget.onSelectedItem(item),
              focusColor: Colors.transparent,
              hoverColor: Colors.transparent,
            ),
          );
        }

        return Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent, // 移除分割线
          ),
          child: ExpansionTile(
            key: ValueKey('${item.id}_expansion'),
            initiallyExpanded: true,
            onExpansionChanged: (expanded) {
              widget.onSelectedItem(item);
            },
            title: widget.builder(level, item),
            showTrailingIcon: false,
            childrenPadding: EdgeInsets.only(left: 20),
            children: item.children.map((child) => buildExpansionTile(level + 1, child as T)).toList(),
          ),
        );
      },
    );
  }
}
