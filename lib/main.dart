import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:renovation/pages/cost.dart';
import 'package:renovation/untils/global.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final dir = await getApplicationDocumentsDirectory();
  Hive.init(dir.path);
  runApp(const MyApp());
}

const border = 4.0;

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final colorSchema = ColorScheme.fromSeed(seedColor: Colors.blueAccent);
    return GetMaterialApp(
      title: 'Flutter Demo',

      theme: ThemeData(
        colorScheme: colorSchema,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(border)),
          enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(border)),
          focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(border)),
          // prefixStyle: TextStyle(fontSize: 18)
        ),
        segmentedButtonTheme: SegmentedButtonThemeData(
          style: ButtonStyle(
            shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadius.circular(border))),
            padding: WidgetStatePropertyAll(EdgeInsets.symmetric(vertical: 12)),
          ),
        ),
        filledButtonTheme: FilledButtonThemeData(
          style: ButtonStyle(
            shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadius.circular(border))),
            backgroundColor: WidgetStatePropertyAll(colorSchema.primary),
          ),
        ),
        textTheme: TextTheme(
          bodyLarge: TextStyle(fontSize: 20),
          bodyMedium: TextStyle(fontSize: 18),
          bodySmall: TextStyle(fontSize: 16),
          titleLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.w800),
          titleMedium: TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
          titleSmall: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
        ),
      ),
      home: const Cost(),
    );
  }
}
