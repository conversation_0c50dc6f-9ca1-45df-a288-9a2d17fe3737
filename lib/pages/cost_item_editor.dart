import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import 'cost.dart';

class CostItemEditor extends StatefulWidget {
  final CostItem? item;

  const CostItemEditor({super.key, this.item});

  @override
  State<StatefulWidget> createState() => _CostItemEditorState();
}

class _CostItemEditorState extends State<CostItemEditor> {
  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController(text: widget.item?.title);
    final quantityController = TextEditingController(text: widget.item?.quantity.value?.toDouble().toPrecision(2).toString());
    final unitPriceController = TextEditingController(text: widget.item?.unitPrice.value?.toDouble().toPrecision(2).toString());
    final amountController = TextEditingController(text: widget.item?.lineAmount.value.toDouble().toPrecision(2).toString());
    final descriptionController = TextEditingController(text: widget.item?.description);
    final item =
        widget.item ??
        CostItem(
          Uuid().v4(),
          "",
          false.obs,
          children: <CostItem>[].obs,
          checked: RxnBool(false),
          quantity: RxnNum(null),
          unitPrice: RxnNum(null),
          lineAmount: Rx(0),
        );
    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: SingleChildScrollView(
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("名称"),
                SizedBox(height: 4),
                TextField(
                  decoration: InputDecoration(isDense: true),
                  controller: nameController,
                  onChanged: (v) {
                    item.title = v;
                  },
                ),
                SizedBox(height: 16),
                Text("数量"),
                SizedBox(height: 4),
                TextField(
                  decoration: InputDecoration(isDense: true),
                  controller: quantityController,
                  onChanged: (v) {
                    item.quantity.value = double.tryParse(v);
                  },
                ),
                SizedBox(height: 16),
                Text("单价"),
                SizedBox(height: 4),
                TextField(
                  decoration: InputDecoration(isDense: true),
                  controller: unitPriceController,
                  onChanged: (v) {
                    item.unitPrice.value = double.tryParse(v);
                  },
                ),
                SizedBox(height: 16),
                Text("金额"),
                SizedBox(height: 4),
                TextField(
                  decoration: InputDecoration(isDense: true),
                  controller: amountController,
                  onChanged: (v) {
                    item.lineAmount.value = double.tryParse(v) ?? 0;
                  },
                ),
                SizedBox(height: 16),
                Text("备注"),
                SizedBox(height: 4),
                TextField(
                  decoration: InputDecoration(isDense: true),
                  maxLines: 3,
                  controller: descriptionController,
                  onChanged: (v) {
                    item.description = v;
                  },
                ),
                SizedBox(height: 48),
                SizedBox(
                  width: double.maxFinite,
                  height: 60,
                  child: FilledButton(
                    onPressed: () {
                      Get.back(result: item);
                    },

                    child: Text("保存", style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Theme.of(context).colorScheme.onPrimary)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
