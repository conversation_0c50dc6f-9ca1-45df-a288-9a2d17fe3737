import 'package:flutter/material.dart';

class Planner extends StatefulWidget {
  const Planner({super.key});

  @override
  State<StatefulWidget> createState() => _PlannerState();
}

class _PlannerState extends State<Planner> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 48,
                    child: TextField(
                      decoration: InputDecoration(
                        prefixText: "房屋面积",
                        floatingLabelBehavior: FloatingLabelBehavior.always,
                        hintText: "套内",
                        suffixText: "m²",
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                  Container(height: 8),
                  Text("户型", style: Theme.of(context).textTheme.titleSmall),
                  Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 48,
                          child: TextField(
                            decoration: InputDecoration(
                              floatingLabelBehavior: FloatingLabelBehavior.always,
                              hintText: "3",
                              suffix: Padding(padding: EdgeInsets.only(left: 8), child: Text("室")),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ),
                      Container(width: 8),
                      Expanded(
                        child: SizedBox(
                          height: 48,
                          child: TextField(
                            decoration: InputDecoration(
                              floatingLabelBehavior: FloatingLabelBehavior.always,
                              hintText: "2",
                              suffix: Padding(padding: EdgeInsets.only(left: 8), child: Text("厅")),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ),
                      Container(width: 8),
                      Expanded(
                        child: SizedBox(
                          height: 48,
                          child: TextField(
                            decoration: InputDecoration(
                              floatingLabelBehavior: FloatingLabelBehavior.always,
                              hintText: "1",
                              suffix: Padding(padding: EdgeInsets.only(left: 8), child: Text("厨")),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ),
                      Container(width: 8),
                      Expanded(
                        child: SizedBox(
                          height: 48,
                          child: TextField(
                            decoration: InputDecoration(
                              floatingLabelBehavior: FloatingLabelBehavior.always,
                              hintText: "1",
                              suffix: Padding(padding: EdgeInsets.only(left: 8), child: Text("卫")),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Text("装修档次", style: Theme.of(context).textTheme.titleSmall),
                  SizedBox(
                    width: double.maxFinite,
                    child: SegmentedButton(
                      segments: [
                        ButtonSegment(value: "0", label: Text("简装")),
                        ButtonSegment(value: "1", label: Text("普通")),
                        ButtonSegment(value: "2", label: Text("中高")),
                        ButtonSegment(value: "3", label: Text("豪华")),
                      ],
                      selected: {"1"},
                      multiSelectionEnabled: false,
                      onSelectionChanged: (value) {},
                    ),
                  ),
                  Text("地区", style: Theme.of(context).textTheme.titleSmall),
                  SizedBox(height: 48, child: TextField()),
                  Container(height: 40),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 2 / 3,
                        height: 48,
                        child: FilledButton(onPressed: () {}, child: Text("生成预算表")),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
