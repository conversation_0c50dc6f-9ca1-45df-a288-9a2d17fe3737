import 'package:flutter/cupertino.dart' show CupertinoAlertDialog, showCupertinoDialog;
import 'package:flutter/material.dart' hide AlertDialog;
import 'package:flutter/cupertino.dart' show CupertinoAlertDialog;
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:renovation/components/expandable_list.dart';
import 'package:renovation/pages/cost_item_editor.dart';

class Cost extends StatefulWidget {
  const Cost({super.key});

  @override
  State<StatefulWidget> createState() => _Cost();
}

class _Cost extends State<Cost> {
  final _items = RxList<CostItem>();
  final _selectedKey = RxnString(null);
  final _controllers = <String, TextEditingController>{};
  final _costData = Rx<Box<CostItem>?>(null);

  @override
  void initState() {
    Hive.openBox<CostItem>("cost").then((v) => _costData.value = v);
    super.initState();
  }

  @override
  void dispose() {
    for (final item in _controllers.values) {
      item.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => _costData.value == null
            ? Center(child: CircularProgressIndicator())
            : Stack(
                fit: StackFit.expand,
                children: [
                  Positioned.fill(
                    child: ExpandableList<CostItem>(
                      items: _items,
                      onSelectedItem: (item) {
                        if (_selectedKey.value == item.id && item.children.isEmpty) {
                          _selectedKey.value = null;
                        } else {
                          _selectedKey.value = item.id;
                        }
                      },
                      builder: (level, item) {
                        return Obx(
                              () => Column(
                                children: [
                                  Row(
                                    children: [
                                      Checkbox(
                                        tristate: item.children.isNotEmpty,
                                        value: () {
                                          if (item.children.isEmpty) {
                                            return item.checked.value;
                                          }
                                          if (item.children.every((i) => i.checked.value == true)) {
                                            return true;
                                          }
                                          if (item.children.every((i) => i.checked.value == false)) {
                                            return false;
                                          }
                                          return null;
                                        }(),
                                        onChanged: item.children.isEmpty ? (v) => item.checked.value = v : null,
                                      ),
                                      Text(
                                        item.title,
                                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                          decoration: item.checked.value == true ? TextDecoration.lineThrough : null,
                                          decorationColor: Theme.of(context).colorScheme.onSurfaceVariant,
                                          decorationThickness: 2,
                                          color: item.checked.value == true
                                              ? Theme.of(context).colorScheme.onSurfaceVariant
                                              : Theme.of(context).colorScheme.onSurface,
                                        ),
                                      ),
                                      Spacer(),
                                      GestureDetector(
                                        onTap: () {},
                                        child: Obx(
                                          () => GestureDetector(
                                            child: Text(
                                              item.lineAmount.value.toDouble().toPrecision(2).toString(),
                                              style: Theme.of(context).textTheme.bodyLarge?.copyWith(decoration: TextDecoration.underline),
                                            ),
                                            onTap: () {
                                              Get.to(() => CostItemEditor(item: item));
                                            },
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Obx(
                                        () => Visibility(
                                          visible: _selectedKey.value == item.id,
                                          child: GestureDetector(
                                            child: Icon(Icons.add_circle_outline_rounded),
                                            onTap: () async {
                                              final newItem = (await Get.to(() => CostItemEditor())) as CostItem?;
                                              if (newItem != null) {
                                                item.children.add(newItem);
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                      // TextField(),
                                    ],
                                  ),
                                  item.children.isEmpty && _selectedKey.value == item.id
                                      ? Padding(
                                          padding: EdgeInsets.only(bottom: 20),
                                          child: Row(mainAxisAlignment: MainAxisAlignment.end, children: []),
                                        )
                                      : SizedBox.shrink(),
                                ],
                              ),
                            )
                            as Widget;
                      },
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FilledButton(onPressed: () {}, child: Text("删除选中")),
                        SizedBox(width: 12),
                        FilledButton(
                          onPressed: () async {
                            final item = (await Get.to(() => CostItemEditor())) as CostItem?;
                            if (item != null) {
                              _items.add(item);
                            }
                          },
                          child: Text("添加大类"),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

class CostItem extends LevelItem {
  String title;
  RxBool calculating;
  String? description;

  RxnNum quantity = RxnNum(null);
  RxnNum unitPrice = RxnNum(null);
  Rx<double> lineAmount;
  CostItem? parent;

  CostItem(
    String id,
    this.title,
    this.calculating, {
    this.description,
    required this.quantity,
    required this.unitPrice,
    required this.lineAmount,
    required RxList<CostItem> children,
    required RxnBool checked,
  }) : super(id, children, checked) {
    // 监听子节点列表变化
    children.listen((_) {
      // 为新添加的子节点设置父节点引用
      for (var child in children) {
        (child).parent = this;
      }
      if (children.isNotEmpty) {
        quantity.value = null;
        unitPrice.value = null;
      }
      updateTotalAmount();
    });

    // 监听自身lineAmount变化
    lineAmount.listen((_) {
      // 通知父节点更新
      if (parent != null) {
        parent!.updateTotalAmount();
      }
    });
  }

  // 更新总金额
  void updateTotalAmount() {
    if (children.isEmpty) return;

    double total = 0;
    // 计算所有子节点的金额总和
    for (var child in children) {
      var costChild = child as CostItem;
      total += costChild.lineAmount.value;
    }

    // 更新自身金额，但避免无限循环
    if (lineAmount.value != total) {
      lineAmount.value = total;
    }
  }
}
